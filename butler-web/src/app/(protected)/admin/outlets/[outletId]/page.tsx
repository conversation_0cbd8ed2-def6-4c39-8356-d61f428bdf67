"use client";

import { useEffect, useRef, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import OutletQrCode from "@/components/custom/outlet/OutletQrCode";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Phone,
  Calendar,
  Store,
  ChevronLeft,
  IndianRupee,
  PlusCircle,
} from "lucide-react";
import { getSingleOutlet } from "@/server/admin";
import { Outlet } from "@/app/type";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { Icon } from "@iconify/react/dist/iconify.js";
const OutletViewPage = () => {
  const router = useRouter();
  const params = useSearchParams();
  const isViewPage = params ? params.get("view") === "true" : false;
  const { outletId } = useParams() || {};
  const [outlet, setOutlet] = useState<Outlet | null>(null);
  const [loading, setLoading] = useState(true);
  const qrCodeRef = useRef(null);

  useEffect(() => {
    // Mock API call - replace with actual API call
    const fetchOutletData = async () => {
      try {
        const res = await getSingleOutlet(outletId as string);

        setOutlet(res.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching outlet data:", error);
        setLoading(false);
      }
    };

    if (outletId) {
      fetchOutletData();
    }
  }, [outletId]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg font-medium">Loading outlet information...</div>
      </div>
    );
  }

  if (!outlet) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg font-medium text-red-500">Outlet not found</div>
      </div>
    );
  }

  if (isViewPage) {
    return (
      <div className="absolute inset-0 bg-white min-h-screen flex flex-col items-center justify-center p-6 z-50">
        {/* Download Button */}
        <div className="absolute top-10 right-10">
          <div
            className="flex justify-center gap-2 cursor-pointer"
            // onClick={handleDownload}
            onClick={() => window.print()}
          >
            <Icon icon="line-md:download-loop" width="30" height="30" />
          </div>
        </div>

        {/* Page Content */}
        <div ref={qrCodeRef}>
          {/* Absolute Logo */}
          <Image
            src="/ai_image.png"
            alt="Outlet Logo"
            width={200}
            height={200}
            className="absolute bottom-20 right-10"
          />

          <div className="max-w-md w-full mx-auto text-center space-y-8">
            {/* Header */}
            <div className="space-y-4">
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome to {outlet?.name}
              </h1>
              <p className="text-lg text-gray-600">
                Scan the QR code to chat with your personal AI butler
              </p>
            </div>

            {/* QR Code Section */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border-2 border-blue-100 flex flex-col items-center">
              <div className="mb-6">
                <OutletQrCode outletId={String(outletId)} onlyQr />
              </div>
              <p className="text-sm text-gray-500">
                Point your camera at the QR code to start ordering
              </p>
            </div>

            {/* Features Section */}
            <div className="grid grid-cols-2 gap-4 text-center mt-8">
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-semibold text-blue-900">Easy Ordering</h3>
                <p className="text-sm text-blue-700">
                  Order directly from your phone
                </p>
              </div>
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-semibold text-blue-900">AI Assistant</h3>
                <p className="text-sm text-blue-700">
                  Get personalized recommendations
                </p>
              </div>
            </div>

            {/* Footer */}
            <div className="text-sm text-gray-500">Powered by Butler AI</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <Button className="mb-3" onClick={() => router.back()}>
        <ChevronLeft /> Back
      </Button>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="shadow-md">
            <CardHeader className="border-b pb-4">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-2xl font-bold">
                    {outlet.name}
                  </CardTitle>
                  <p className="text-gray-500 mt-1">
                    {typeof outlet?.foodChain === "string"
                      ? outlet?.foodChain
                      : outlet?.foodChain?.name}
                  </p>
                </div>
                <Badge
                  variant={outlet.isCloudKitchen ? "default" : "outline"}
                  className={outlet.isCloudKitchen ? "bg-blue-500" : ""}
                >
                  {outlet.isCloudKitchen
                    ? "Cloud Kitchen"
                    : "Physical Location"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Address</h3>
                    <p className="text-gray-600">{outlet.address}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Contact</h3>
                    <p className="text-gray-600">{outlet.contact}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Store className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Food Chain</h3>
                    <p className="text-gray-600">
                      {typeof outlet?.foodChain === "string"
                        ? outlet?.foodChain
                        : outlet?.foodChain?.name}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Created</h3>
                    <p className="text-gray-600">
                      {formatDate(outlet.createdAt)}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Last updated: {formatDate(outlet.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="shadow-md">
            <CardHeader>
              <CardTitle className="text-lg font-medium">
                Outlet QR Code
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              <div className="mb-4 p-4 bg-white rounded-lg shadow-sm">
                <OutletQrCode outletId={String(outletId)} />
              </div>
              <p className="text-sm text-gray-500 text-center">
                Scan this QR code to talk with Outlet&apos;s Butler
              </p>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle>Dishes</CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="h-8"
                onClick={() =>
                  router.push(`/admin/dishes?outletId=${outletId}`)
                }
              >
                <PlusCircle className="h-4 w-4 mr-1" />
                Add Dish
              </Button>
            </CardHeader>
            <CardContent className="flex h-[400px] overflow-y-scroll flex-col">
              {outlet.dishes && outlet.dishes.length > 0 ? (
                outlet.dishes.map((dish) => (
                  <div
                    className="m-2 p-2 border rounded-md flex justify-between gap-2 w-full h-fit"
                    key={dish._id}
                  >
                    {dish.name}{" "}
                    <div className="flex items-center">
                      <IndianRupee className="h-4 w-4" />
                      {dish.price}
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <p>No dishes added to this outlet yet</p>
                  <Button
                    variant="link"
                    onClick={() =>
                      router.push(`/admin/dishes?outletId=${outletId}`)
                    }
                  >
                    Add dishes
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OutletViewPage;
