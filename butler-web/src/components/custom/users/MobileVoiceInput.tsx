"use client";

import React, { useState, useRef, useCallback, useEffect } from "react";
import { Mic, Square, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";

interface MobileVoiceInputProps {
  onTranscription: (text: string) => void;
  language?: string;
  disabled?: boolean;
  className?: string;
}

interface RecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  error: string | null;
  duration: number;
}

const MobileVoiceInput: React.FC<MobileVoiceInputProps> = ({
  onTranscription,
  language = "en-US",
  disabled = false,
  className = "",
}) => {
  const [state, setState] = useState<RecordingState>({
    isRecording: false,
    isProcessing: false,
    error: null,
    duration: 0,
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }

    if (mediaRecorderRef.current) {
      mediaRecorderRef.current = null;
    }

    audioChunksRef.current = [];
  }, []);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current?.state === "recording") {
      mediaRecorderRef.current.stop();
    }

    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
  }, []);

  // Process recorded audio
  const processAudio = useCallback(async () => {
    try {
      console.log("📱 Processing audio...", {
        chunks: audioChunksRef.current.length,
        mimeType: mediaRecorderRef.current?.mimeType,
      });

      if (audioChunksRef.current.length === 0) {
        throw new Error("No audio data recorded");
      }

      // Create audio blob with proper MIME type
      const mimeType = mediaRecorderRef.current?.mimeType || "audio/webm";
      const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });

      console.log("📱 Audio blob created:", {
        size: audioBlob.size,
        type: audioBlob.type,
      });

      // Check if audio is too short (less than 500 bytes for mobile)
      if (audioBlob.size < 500) {
        throw new Error(
          "Recording too short - please speak for at least 1 second"
        );
      }

      // Create form data for API call
      const formData = new FormData();

      // Use proper filename extension based on MIME type
      let filename = "recording.webm";
      if (mimeType.includes("mp4")) filename = "recording.mp4";
      else if (mimeType.includes("wav")) filename = "recording.wav";
      else if (mimeType.includes("ogg")) filename = "recording.ogg";

      formData.append("audio", audioBlob, filename);
      formData.append("language", language);

      console.log("📱 Sending audio to transcription API...", {
        filename,
        size: audioBlob.size,
        language,
      });

      // Send to transcription API with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      try {
        const response = await fetch("/api/transcribe-audio", {
          method: "POST",
          body: formData,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log("📱 Transcription API response:", result);

        if (result.success && result.transcript) {
          console.log("📱 Transcription successful:", result.transcript);
          onTranscription(result.transcript.trim());
        } else {
          console.error("📱 Transcription failed:", result);
          throw new Error(
            result.error || result.details || "Transcription failed"
          );
        }
      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError instanceof Error && fetchError.name === "AbortError") {
          throw new Error("Transcription timeout - please try again");
        }
        throw fetchError;
      }
    } catch (error) {
      console.error("📱 Audio processing error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Processing failed";
      setState((prev) => ({ ...prev, error: errorMessage }));
      toast.error(`Transcription failed: ${errorMessage}`);
    } finally {
      setState((prev) => ({ ...prev, isProcessing: false }));
      cleanup();
    }
  }, [language, onTranscription, cleanup]);

  // Start recording
  const startRecording = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, error: null, duration: 0 }));

      // Check if MediaRecorder is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Audio recording not supported on this device");
      }

      if (!window.MediaRecorder) {
        throw new Error("MediaRecorder not supported on this browser");
      }

      console.log("🎤 Starting mobile audio recording...");

      // Request microphone permission with mobile-optimized constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          // Remove specific constraints that might not be supported on mobile
          // sampleRate: 16000,
          // channelCount: 1,
        },
      });

      streamRef.current = stream;
      audioChunksRef.current = [];

      // Create MediaRecorder with mobile-compatible format
      let mimeType = "audio/webm";

      // Try different formats in order of preference for mobile
      const supportedTypes = [
        "audio/webm;codecs=opus",
        "audio/webm",
        "audio/mp4",
        "audio/mpeg",
        "audio/wav",
        "audio/ogg",
      ];

      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          mimeType = type;
          console.log(`📱 Using MIME type: ${mimeType}`);
          break;
        }
      }

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType,
        // Add mobile-friendly options
        audioBitsPerSecond: 128000, // Lower bitrate for mobile
      });
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        setState((prev) => ({
          ...prev,
          isRecording: false,
          isProcessing: true,
        }));
        await processAudio();
      };

      mediaRecorder.onerror = (event) => {
        console.error("📱 MediaRecorder error:", event);
        const errorMsg = `Recording error: ${
          event.error?.message || "Unknown error"
        }`;
        setState((prev) => ({
          ...prev,
          isRecording: false,
          error: errorMsg,
        }));
        toast.error(errorMsg);
        cleanup();
      };

      // Start recording with mobile-optimized settings
      try {
        mediaRecorder.start(1000); // Collect data every 1 second for mobile stability
        console.log("📱 MediaRecorder started successfully");
      } catch (startError) {
        console.error("📱 Failed to start MediaRecorder:", startError);
        const errorMsg =
          startError instanceof Error
            ? startError.message
            : "Unknown start error";
        throw new Error(`Failed to start recording: ${errorMsg}`);
      }

      setState((prev) => ({ ...prev, isRecording: true }));
      console.log("📱 Recording state updated to active");

      // Start duration counter
      durationIntervalRef.current = setInterval(() => {
        setState((prev) => ({ ...prev, duration: prev.duration + 1 }));
      }, 1000);

      // Auto-stop after 30 seconds to prevent long recordings
      setTimeout(() => {
        if (mediaRecorderRef.current?.state === "recording") {
          stopRecording();
        }
      }, 30000);
    } catch (error) {
      console.error("Failed to start recording:", error);
      let errorMessage = "Failed to access microphone";

      if (error instanceof Error) {
        if (error.name === "NotAllowedError") {
          errorMessage = "Microphone permission denied";
        } else if (error.name === "NotFoundError") {
          errorMessage = "No microphone found";
        } else if (error.name === "NotSupportedError") {
          errorMessage = "Recording not supported";
        }
      }

      setState((prev) => ({ ...prev, error: errorMessage }));
      toast.error(errorMessage);
      cleanup();
    }
  }, [cleanup, processAudio, stopRecording]);

  // Toggle recording
  const toggleRecording = useCallback(() => {
    if (state.isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [state.isRecording, startRecording, stopRecording]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Format duration
  // const formatDuration = (seconds: number) => {
  //   const mins = Math.floor(seconds / 60);
  //   const secs = seconds % 60;
  //   return `${mins}:${secs.toString().padStart(2, "0")}`;
  // };

  return (
    <div className={`flex flex-col items-center gap-2 ${className}`}>
      <Button
        onClick={toggleRecording}
        disabled={disabled || state.isProcessing}
        variant={state.isRecording ? "destructive" : "outline"}
        size="lg"
        className={`
          relative h-12 w-12 rounded-full p-0 transition-all duration-200
          ${
            state.isRecording ? "animate-pulse bg-red-500 hover:bg-red-600" : ""
          }
          ${state.isProcessing ? "cursor-not-allowed opacity-50" : ""}
        `}
      >
        {state.isProcessing ? (
          <Loader2 className="h-5 w-5 animate-spin" />
        ) : state.isRecording ? (
          <Square className="h-5 w-5 fill-current" />
        ) : (
          <Mic className="h-5 w-5" />
        )}
      </Button>
    </div>
  );
};

export default MobileVoiceInput;
