"use client";
import React, { createContext, useContext, useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";

interface SocketContextType {
  adminSocket: Socket | null;
  customerSocket: Socket | null;
  adminSocketError: string | null;
  customerSocketError: string | null;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider = ({ children }: { children: React.ReactNode }) => {
  const [adminSocket, setAdminSocket] = useState<Socket | null>(null);
  const [customerSocket, setCustomerSocket] = useState<Socket | null>(null);
  const [adminSocketError, setAdminSocketError] = useState<string | null>(null);
  const [customerSocketError, setCustomerSocketError] = useState<string | null>(
    null
  );

  useEffect(() => {
    const authToken = localStorage.getItem("auth-token");
    const userToken = localStorage.getItem("user-token");

    const socketOptions = {
      transports: ["websocket", "polling"],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      withCredentials: true,
      timeout: 10000, // Increase timeout to 10 seconds
    };

    if (authToken) {
      console.log(
        "Attempting to connect admin socket to",
        `${process.env.NEXT_PUBLIC_BASE_URL}/admin-orders`
      );

      const adminSocketInstance = io(
        `${process.env.NEXT_PUBLIC_BASE_URL}/admin-orders`,
        {
          ...socketOptions,
          auth: { token: authToken },
        }
      );

      adminSocketInstance.on("connect", () => {
        console.log("Admin socket connected successfully");
        setAdminSocketError(null);
      });

      adminSocketInstance.on("connect_error", (error) => {
        const errorMessage = `Admin socket connection error: ${
          error.message
        }. Details: ${JSON.stringify(error)}`;
        console.error(errorMessage);
        setAdminSocketError(errorMessage);
      });

      adminSocketInstance.on("disconnect", (reason) => {
        console.warn("Admin socket disconnected:", reason);
        if (reason === "io server disconnect") {
          // The disconnection was initiated by the server, reconnect manually
          adminSocketInstance.connect();
        }
      });

      adminSocketInstance.on("error", (error) => {
        console.error("Admin socket general error:", error);
        setAdminSocketError(`Admin socket error: ${error}`);
      });

      adminSocketInstance.on("error", (error) => {
        const errorMessage = `Admin socket general error: ${error}`;
        console.error(errorMessage);
        setAdminSocketError(errorMessage);
      });

      setAdminSocket(adminSocketInstance);

      return () => {
        console.log("Disconnecting admin socket");
        adminSocketInstance.disconnect();
      };
    }

    if (userToken) {
      console.log(
        "Attempting to connect customer socket to",
        `${process.env.NEXT_PUBLIC_BASE_URL}/customer-orders`
      );

      const customerSocketInstance = io(
        `${process.env.NEXT_PUBLIC_BASE_URL}/customer-orders`,
        {
          ...socketOptions,
          auth: { token: userToken },
        }
      );

      customerSocketInstance.on("connect", () => {
        console.log("Customer socket connected successfully");
        setCustomerSocketError(null);
      });

      customerSocketInstance.on("connect_error", (error) => {
        const errorMessage = `Customer socket connection error: ${
          error.message
        }. Details: ${JSON.stringify(error)}`;
        console.error(errorMessage);
        setCustomerSocketError(errorMessage);
      });

      customerSocketInstance.on("disconnect", (reason) => {
        console.warn("Customer socket disconnected:", reason);
        if (reason === "io server disconnect") {
          // The disconnection was initiated by the server, reconnect manually
          customerSocketInstance.connect();
        }
      });

      customerSocketInstance.on("error", (error) => {
        const errorMessage = `Customer socket general error: ${error}`;
        console.error(errorMessage);
        setCustomerSocketError(errorMessage);
      });

      setCustomerSocket(customerSocketInstance);

      return () => {
        console.log("Disconnecting customer socket");
        customerSocketInstance.disconnect();
      };
    }
  }, []);

  return (
    <SocketContext.Provider
      value={{
        adminSocket,
        customerSocket,
        adminSocketError,
        customerSocketError,
      }}
    >
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocket must be used within a SocketProvider");
  }
  return context;
};
