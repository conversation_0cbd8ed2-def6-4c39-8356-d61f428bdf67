import express from "express";
import {
  register,
  registerWith<PERSON>hone,
  login,
  loginWithPhone,
  getDishes,
  getRecommendations,
  createOrder,
  getOrderHistory,
  getOrderStatus,
  getConversation,
  getAllConversations,
  deleteConversation,
  getUserDetails,
  updateUserDetails,
  updatePassword,
  cancelOrder,
  updateOrderItems,
  getOutlets,
  getOutletRecommendations,
  googleAuth,
  googleCallback,
  verifyGoogleToken,
} from "../controllers/user-controller.js";
import {
  validateUserCoupon,
  applyUserCoupon,
  getActiveCoupons,
} from "../controllers/user-coupon-controller.js";
import { getSimilarDishes } from "../controllers/vector-controller.js";
import { authenticateToken } from "../middlewares/auth.js";

const router = express.Router();

// Auth routes
router.post("/user/register", register);
router.post("/user/register-phone", registerWithPhone);
router.post("/user/login", login);
router.post("/user/login-phone", loginWithPhone);

// Dish routes
router.get(
  "/user/dishes",
  //  authenticateToken,
  getDishes
);
router.get(
  "/user/conversation",
  //  authenticateToken,
  getRecommendations
);

router.get(
  "/user/get_conversation",
  //  authenticateToken,
  getConversation
);

router.get(
  "/user/get_all_conversations",
  //  authenticateToken,
  getAllConversations
);

router.delete(
  "/user/delete_conversation",
  //  authenticateToken,
  deleteConversation
);

router.get("/user/user_profile/:id", getUserDetails);

router.put("/user/update_profile/:id", updateUserDetails);

router.put("/user/update_password/:id", authenticateToken, updatePassword);

// Order routes
router.post("/user/create-order", authenticateToken, createOrder);
router.get("/user/orders", authenticateToken, getOrderHistory);
router.get("/user/orders/:orderId", authenticateToken, getOrderStatus);
router.put("/user/orders/:orderId/cancel", authenticateToken, cancelOrder);
router.put(
  "/user/orders/:orderId/update-items",
  authenticateToken,
  updateOrderItems
);

// Coupon routes
router.post("/user/coupons/validate", authenticateToken, validateUserCoupon);
router.post("/user/coupons/apply", authenticateToken, applyUserCoupon);
router.get("/user/coupons/active", authenticateToken, getActiveCoupons);

// Vector search route
router.get("/user/vector/similar-dishes", getSimilarDishes);

// Outlet routes (public access for browsing)
router.get("/user/outlets", getOutlets);
router.get("/user/outlets/recommendations", getOutletRecommendations);

// Google OAuth routes
router.get("/user/auth/google", googleAuth);
router.get("/user/auth/google/callback", googleCallback);
router.post("/user/auth/google/verify", verifyGoogleToken);

export default router;
