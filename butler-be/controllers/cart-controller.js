import Cart from "../models/Cart.js";
import Dish from "../models/Dish.js";
import { validateAndApplyOffers } from "../services/offer-validation-service.js";
import { emitCartUpdate } from "../sockets/cartSocket.js";

// Get user's cart
export const getCart = async (req, res) => {
  try {
    const userId = req.user._id;
    const { foodChainId, outletId } = req.query;

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    // Find or create cart
    let cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    }).populate("items.dishId", "name price image isVeg category");

    if (!cart) {
      // Create new empty cart
      cart = new Cart({
        userId,
        foodChainId,
        outletId,
        items: [],
        appliedOffers: [],
        appliedCoupons: [],
      });
      await cart.save();
    }

    // Apply offers automatically
    if (cart.items.length > 0) {
      try {
        const orderData = {
          items: cart.items.map((item) => ({
            dishId: item.dishId._id,
            quantity: item.quantity,
            price: item.price,
          })),
          totalAmount: cart.subtotal,
          foodChainId,
          outletId,
        };

        const offerResult = await validateAndApplyOffers(orderData, userId);

        if (offerResult.success && offerResult.appliedOffers.length > 0) {
          cart.appliedOffers = offerResult.appliedOffers.map((offer) => ({
            offerId: offer.offerId,
            offerName: offer.offerName,
            offerType: offer.offerType,
            discountAmount: offer.discountAmount,
            discountType: offer.discountType,
            applicableItems: offer.applicableItems || [],
          }));
          await cart.save();
        }
      } catch (offerError) {
        console.error("Error applying offers:", offerError);
        // Continue without offers if there's an error
      }
    }

    res.json({
      success: true,
      data: {
        items: cart.items,
        appliedOffers: cart.appliedOffers,
        appliedCoupons: cart.appliedCoupons,
        subtotal: cart.subtotal,
        totalOfferDiscount: cart.totalOfferDiscount,
        totalCouponDiscount: cart.totalCouponDiscount,
        totalDiscount: cart.totalDiscount,
        taxAmount: cart.taxAmount,
        deliveryFee: cart.deliveryFee,
        packagingFee: cart.packagingFee,
        finalTotal: cart.finalTotal,
        updatedAt: cart.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error fetching cart:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching cart",
      error: error.message,
    });
  }
};

// Add item to cart
export const addToCart = async (req, res) => {
  try {
    const userId = req.user._id;
    const { dishId, quantity = 1, foodChainId, outletId } = req.body;

    if (!dishId || !foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Dish ID, food chain ID, and outlet ID are required",
      });
    }

    // Validate dish exists and is available
    const dish = await Dish.findOne({
      _id: dishId,
      foodChain: foodChainId,
      outlets: outletId,
      isAvailable: true,
    });

    if (!dish) {
      return res.status(404).json({
        success: false,
        message: "Dish not found or not available",
      });
    }

    // Find or create cart
    let cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    });

    if (!cart) {
      cart = new Cart({
        userId,
        foodChainId,
        outletId,
        items: [],
        appliedOffers: [],
        appliedCoupons: [],
      });
    }

    // Add item to cart
    await cart.addItem(dish, quantity);

    // Emit real-time cart update
    await emitCartUpdate(req.user._id, foodChainId, outletId, "add");

    // Populate dish details for response
    await cart.populate("items.dishId", "name price image isVeg category");

    res.json({
      success: true,
      message: "Item added to cart successfully",
      data: {
        items: cart.items,
        appliedOffers: cart.appliedOffers,
        appliedCoupons: cart.appliedCoupons,
        subtotal: cart.subtotal,
        totalOfferDiscount: cart.totalOfferDiscount,
        totalCouponDiscount: cart.totalCouponDiscount,
        totalDiscount: cart.totalDiscount,
        taxAmount: cart.taxAmount,
        deliveryFee: cart.deliveryFee,
        packagingFee: cart.packagingFee,
        finalTotal: cart.finalTotal,
        updatedAt: cart.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error adding item to cart:", error);
    res.status(500).json({
      success: false,
      message: "Error adding item to cart",
      error: error.message,
    });
  }
};

// Remove item from cart
export const removeFromCart = async (req, res) => {
  try {
    const userId = req.user._id;
    const { dishId } = req.params;
    const { foodChainId, outletId } = req.query;

    if (!dishId || !foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Dish ID, food chain ID, and outlet ID are required",
      });
    }

    // Find cart
    const cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    });

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: "Cart not found",
      });
    }

    // Remove item from cart
    await cart.removeItem(dishId);

    // Emit real-time cart update
    await emitCartUpdate(req.user._id, foodChainId, outletId, "remove");

    // Populate dish details for response
    await cart.populate("items.dishId", "name price image isVeg category");

    res.json({
      success: true,
      message: "Item removed from cart successfully",
      data: {
        items: cart.items,
        appliedOffers: cart.appliedOffers,
        appliedCoupons: cart.appliedCoupons,
        subtotal: cart.subtotal,
        totalOfferDiscount: cart.totalOfferDiscount,
        totalCouponDiscount: cart.totalCouponDiscount,
        totalDiscount: cart.totalDiscount,
        taxAmount: cart.taxAmount,
        deliveryFee: cart.deliveryFee,
        packagingFee: cart.packagingFee,
        finalTotal: cart.finalTotal,
        updatedAt: cart.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error removing item from cart:", error);
    res.status(500).json({
      success: false,
      message: "Error removing item from cart",
      error: error.message,
    });
  }
};

// Update item quantity in cart
export const updateCartItemQuantity = async (req, res) => {
  try {
    const userId = req.user._id;
    const { dishId } = req.params;
    const { quantity, foodChainId, outletId } = req.body;

    if (!dishId || quantity === undefined || !foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Dish ID, quantity, food chain ID, and outlet ID are required",
      });
    }

    if (quantity < 0) {
      return res.status(400).json({
        success: false,
        message: "Quantity cannot be negative",
      });
    }

    // Find cart
    const cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    });

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: "Cart not found",
      });
    }

    // Update item quantity
    await cart.updateItemQuantity(dishId, quantity);

    // Emit real-time cart update
    await emitCartUpdate(req.user._id, foodChainId, outletId, "update");

    // Populate dish details for response
    await cart.populate("items.dishId", "name price image isVeg category");

    res.json({
      success: true,
      message: "Cart updated successfully",
      data: {
        items: cart.items,
        appliedOffers: cart.appliedOffers,
        appliedCoupons: cart.appliedCoupons,
        subtotal: cart.subtotal,
        totalOfferDiscount: cart.totalOfferDiscount,
        totalCouponDiscount: cart.totalCouponDiscount,
        totalDiscount: cart.totalDiscount,
        taxAmount: cart.taxAmount,
        deliveryFee: cart.deliveryFee,
        packagingFee: cart.packagingFee,
        finalTotal: cart.finalTotal,
        updatedAt: cart.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error updating cart item quantity:", error);
    res.status(500).json({
      success: false,
      message: "Error updating cart item quantity",
      error: error.message,
    });
  }
};

// Clear cart
export const clearCart = async (req, res) => {
  try {
    const userId = req.user._id;
    const { foodChainId, outletId } = req.query;

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    // Find cart
    const cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    });

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: "Cart not found",
      });
    }

    // Clear cart items
    await cart.clearItems();

    res.json({
      success: true,
      message: "Cart cleared successfully",
      data: {
        items: [],
        appliedOffers: [],
        appliedCoupons: [],
        subtotal: 0,
        totalOfferDiscount: 0,
        totalCouponDiscount: 0,
        totalDiscount: 0,
        taxAmount: 0,
        deliveryFee: 0,
        packagingFee: 0,
        finalTotal: 0,
        updatedAt: cart.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error clearing cart:", error);
    res.status(500).json({
      success: false,
      message: "Error clearing cart",
      error: error.message,
    });
  }
};

// Sync cart from localStorage to backend
export const syncCart = async (req, res) => {
  try {
    const userId = req.user._id;
    const { items, foodChainId, outletId, appliedOffers = [] } = req.body;

    if (!foodChainId || !outletId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID and outlet ID are required",
      });
    }

    // Find or create cart
    let cart = await Cart.findOne({
      userId,
      foodChainId,
      outletId,
    });

    if (!cart) {
      cart = new Cart({
        userId,
        foodChainId,
        outletId,
        items: [],
        appliedOffers: [],
        appliedCoupons: [],
      });
    }

    // Clear existing items and sync new ones
    cart.items = [];

    if (items && items.length > 0) {
      for (const item of items) {
        // Validate dish exists
        const dish = await Dish.findOne({
          _id: item._id || item.dishId,
          foodChain: foodChainId,
          outlets: outletId,
          isAvailable: true,
        });

        if (dish) {
          cart.items.push({
            dishId: dish._id,
            dishName: dish.name,
            quantity: item.quantity || 1,
            price: dish.price,
            customizations: item.customizations || {},
          });
        }
      }
    }

    // Sync applied offers if provided
    if (appliedOffers && appliedOffers.length > 0) {
      cart.appliedOffers = appliedOffers;
    }

    await cart.save();

    // Populate dish details for response
    await cart.populate("items.dishId", "name price image isVeg category");

    res.json({
      success: true,
      message: "Cart synced successfully",
      data: {
        items: cart.items,
        appliedOffers: cart.appliedOffers,
        appliedCoupons: cart.appliedCoupons,
        subtotal: cart.subtotal,
        totalOfferDiscount: cart.totalOfferDiscount,
        totalCouponDiscount: cart.totalCouponDiscount,
        totalDiscount: cart.totalDiscount,
        taxAmount: cart.taxAmount,
        deliveryFee: cart.deliveryFee,
        packagingFee: cart.packagingFee,
        finalTotal: cart.finalTotal,
        updatedAt: cart.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error syncing cart:", error);
    res.status(500).json({
      success: false,
      message: "Error syncing cart",
      error: error.message,
    });
  }
};
