import Order from "../models/Order.js";
import Conversation from "../models/Conversation.js";
import CartOperation from "../models/CartOperation.js";
import { getUserPreferences } from "./personalization-service.js";

/**
 * Context-aware service for enhanced AI recommendations
 * Provides comprehensive context including cart, order history, and session data
 */

/**
 * Get cart context from localStorage (simulated from frontend data)
 * @param {Array} cartHistory - Cart operations from frontend
 * @returns {Object} - Cart context information
 */
export const getCartContext = (cartHistory = []) => {
  try {
    const cartItems = {};
    let totalItems = 0;
    let totalValue = 0;
    const categories = new Set();
    const cuisines = new Set();

    // Process cart operations to build current cart state
    cartHistory.forEach(operation => {
      if (operation.operation === 'add' && operation.dish) {
        const dishId = operation.dish._id || operation.dish.dishId;
        const quantity = operation.quantity || 1;
        const price = operation.dish.price || 0;
        
        cartItems[dishId] = {
          dish: operation.dish,
          quantity: (cartItems[dishId]?.quantity || 0) + quantity,
          totalPrice: ((cartItems[dishId]?.quantity || 0) + quantity) * price
        };
        
        totalItems += quantity;
        totalValue += price * quantity;
        
        if (operation.dish.category) {
          categories.add(operation.dish.category.name || operation.dish.category);
        }
        if (operation.dish.cuisine) {
          cuisines.add(operation.dish.cuisine);
        }
      } else if (operation.operation === 'remove' && operation.dish) {
        const dishId = operation.dish._id || operation.dish.dishId;
        const quantity = operation.quantity || 1;
        
        if (cartItems[dishId]) {
          cartItems[dishId].quantity = Math.max(0, cartItems[dishId].quantity - quantity);
          if (cartItems[dishId].quantity === 0) {
            delete cartItems[dishId];
          } else {
            cartItems[dishId].totalPrice = cartItems[dishId].quantity * operation.dish.price;
          }
        }
      } else if (operation.operation === 'clear') {
        Object.keys(cartItems).forEach(key => delete cartItems[key]);
        totalItems = 0;
        totalValue = 0;
        categories.clear();
        cuisines.clear();
      }
    });

    return {
      items: cartItems,
      totalItems,
      totalValue,
      categories: Array.from(categories),
      cuisines: Array.from(cuisines),
      isEmpty: totalItems === 0,
      averageItemPrice: totalItems > 0 ? totalValue / totalItems : 0
    };
  } catch (error) {
    console.error("Error getting cart context:", error);
    return {
      items: {},
      totalItems: 0,
      totalValue: 0,
      categories: [],
      cuisines: [],
      isEmpty: true,
      averageItemPrice: 0
    };
  }
};

/**
 * Get session context from recent conversations and orders
 * @param {string} userId - User ID
 * @param {string} outletId - Outlet ID
 * @param {number} sessionHours - Hours to look back for session data
 * @returns {Promise<Object>} - Session context information
 */
export const getSessionContext = async (userId, outletId, sessionHours = 24) => {
  try {
    const sessionStart = new Date(Date.now() - sessionHours * 60 * 60 * 1000);
    
    // Get recent conversations
    const recentConversations = await Conversation.find({
      userId,
      outletId,
      updatedAt: { $gte: sessionStart }
    }).sort({ updatedAt: -1 }).limit(5);

    // Get recent orders
    const recentOrders = await Order.find({
      userId,
      outletId,
      createdAt: { $gte: sessionStart }
    }).populate('items.dishId', 'name category cuisine price').sort({ createdAt: -1 }).limit(3);

    // Get recent cart operations
    const recentCartOps = await CartOperation.find({
      userId,
      createdAt: { $gte: sessionStart }
    }).sort({ createdAt: -1 }).limit(10);

    // Analyze session patterns
    const sessionPatterns = {
      conversationCount: recentConversations.length,
      orderCount: recentOrders.length,
      cartOperationCount: recentCartOps.length,
      lastActivity: recentConversations[0]?.updatedAt || recentOrders[0]?.createdAt,
      isActiveSession: recentConversations.length > 0 || recentOrders.length > 0,
      sessionDuration: sessionHours
    };

    // Extract frequently mentioned items from conversations
    const mentionedItems = new Set();
    recentConversations.forEach(conv => {
      conv.messages.forEach(msg => {
        if (msg.sender === 'user') {
          // Simple keyword extraction for food items
          const foodKeywords = ['pizza', 'burger', 'pasta', 'rice', 'chicken', 'biryani', 'curry', 'naan', 'dal'];
          foodKeywords.forEach(keyword => {
            if (msg.message.toLowerCase().includes(keyword)) {
              mentionedItems.add(keyword);
            }
          });
        }
      });
    });

    return {
      patterns: sessionPatterns,
      recentOrders: recentOrders.slice(0, 2), // Return only 2 most recent
      recentConversations: recentConversations.slice(0, 3), // Return only 3 most recent
      mentionedItems: Array.from(mentionedItems),
      cartOperations: recentCartOps.slice(0, 5) // Return only 5 most recent
    };
  } catch (error) {
    console.error("Error getting session context:", error);
    return {
      patterns: {
        conversationCount: 0,
        orderCount: 0,
        cartOperationCount: 0,
        lastActivity: null,
        isActiveSession: false,
        sessionDuration: sessionHours
      },
      recentOrders: [],
      recentConversations: [],
      mentionedItems: [],
      cartOperations: []
    };
  }
};

/**
 * Get comprehensive context for AI recommendations
 * @param {string} userId - User ID
 * @param {string} outletId - Outlet ID
 * @param {Array} cartHistory - Cart operations from frontend
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - Comprehensive context object
 */
export const getComprehensiveContext = async (userId, outletId, cartHistory = [], options = {}) => {
  try {
    const {
      includePersonalization = true,
      includeSession = true,
      sessionHours = 24
    } = options;

    // Get cart context
    const cartContext = getCartContext(cartHistory);

    // Get user preferences (if personalization is enabled)
    let userPreferences = null;
    if (includePersonalization) {
      userPreferences = await getUserPreferences(userId, outletId);
    }

    // Get session context (if session tracking is enabled)
    let sessionContext = null;
    if (includeSession) {
      sessionContext = await getSessionContext(userId, outletId, sessionHours);
    }

    // Analyze cross-context patterns
    const contextInsights = {
      hasCartItems: !cartContext.isEmpty,
      isReturnCustomer: userPreferences?.totalOrders > 0,
      isActiveSession: sessionContext?.patterns.isActiveSession || false,
      preferredCategories: userPreferences?.preferences.favoriteCategories || [],
      currentCartCategories: cartContext.categories,
      sessionMentions: sessionContext?.mentionedItems || [],
      
      // Recommendation hints
      shouldSuggestComplements: cartContext.totalItems > 0 && cartContext.totalItems < 3,
      shouldSuggestSimilar: userPreferences?.totalOrders > 3,
      shouldSuggestPopular: userPreferences?.totalOrders === 0,
      shouldConsiderBudget: cartContext.totalValue > 0
    };

    return {
      cart: cartContext,
      user: userPreferences,
      session: sessionContext,
      insights: contextInsights,
      timestamp: new Date(),
      userId,
      outletId
    };
  } catch (error) {
    console.error("Error getting comprehensive context:", error);
    return {
      cart: { items: {}, totalItems: 0, totalValue: 0, categories: [], cuisines: [], isEmpty: true },
      user: null,
      session: null,
      insights: {
        hasCartItems: false,
        isReturnCustomer: false,
        isActiveSession: false,
        preferredCategories: [],
        currentCartCategories: [],
        sessionMentions: [],
        shouldSuggestComplements: false,
        shouldSuggestSimilar: false,
        shouldSuggestPopular: true,
        shouldConsiderBudget: false
      },
      timestamp: new Date(),
      userId,
      outletId
    };
  }
};

/**
 * Generate context-aware prompt enhancement
 * @param {Object} context - Comprehensive context from getComprehensiveContext
 * @returns {string} - Context-aware prompt addition
 */
export const generateContextPrompt = (context) => {
  try {
    let contextPrompt = "\n\nCONTEXT-AWARE INFORMATION:\n";

    // Cart context
    if (context.cart && !context.cart.isEmpty) {
      contextPrompt += `Current cart contains ${context.cart.totalItems} items worth ₹${context.cart.totalValue}.\n`;
      contextPrompt += `Cart categories: ${context.cart.categories.join(', ')}.\n`;
      if (context.insights.shouldSuggestComplements) {
        contextPrompt += "Consider suggesting complementary items to complete the meal.\n";
      }
    } else {
      contextPrompt += "Cart is currently empty.\n";
    }

    // User preferences context
    if (context.user && context.user.totalOrders > 0) {
      contextPrompt += `Customer has ${context.user.totalOrders} previous orders.\n`;
      if (context.user.preferences.favoriteCategories.length > 0) {
        const favCategories = context.user.preferences.favoriteCategories.map(cat => cat.category).join(', ');
        contextPrompt += `Favorite categories: ${favCategories}.\n`;
      }
      if (context.insights.shouldSuggestSimilar) {
        contextPrompt += "Consider suggesting items similar to their previous orders.\n";
      }
    } else {
      contextPrompt += "This appears to be a new customer.\n";
      if (context.insights.shouldSuggestPopular) {
        contextPrompt += "Consider suggesting popular or signature items.\n";
      }
    }

    // Session context
    if (context.session && context.session.patterns.isActiveSession) {
      contextPrompt += `Active session with ${context.session.patterns.conversationCount} conversations.\n`;
      if (context.session.mentionedItems.length > 0) {
        contextPrompt += `Recently mentioned: ${context.session.mentionedItems.join(', ')}.\n`;
      }
    }

    contextPrompt += "\nUse this context to provide more relevant and personalized recommendations.\n";

    return contextPrompt;
  } catch (error) {
    console.error("Error generating context prompt:", error);
    return "\n\nProvide helpful recommendations based on the available menu.\n";
  }
};
